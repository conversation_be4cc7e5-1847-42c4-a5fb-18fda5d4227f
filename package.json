{"name": "mvc-boilerplate", "version": "1.0.0", "description": "Boilerplate para estrutura MVC em JavaScript com PostgreSQL e testes", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:coverage": "jest --coverage", "init-db": "node scripts/runSQLScript.js"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^10.0.0", "ejs": "^3.1.10", "express": "^4.21.2", "pg": "^8.7.1"}, "devDependencies": {"jest": "^27.5.1", "nodemon": "^2.0.22", "supertest": "^6.1.3"}, "directories": {"test": "tests"}, "keywords": [], "author": "", "license": "ISC"}