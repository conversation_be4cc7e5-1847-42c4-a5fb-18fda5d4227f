-- init.sql

-- <PERSON><PERSON><PERSON> extensão para suportar UUIDs, se ainda não estiver ativada
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>riar tabela de usuários com UUID como chave primária
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL
);

-- Inserir 20 usuários com nomes e emails aleatórios
INSERT INTO users (name, email)
VALUES 
  ('<PERSON>', '<EMAIL>'),
  ('<PERSON>', '<EMAIL>'),
  ('<PERSON>', '<EMAIL>'),
  ('<PERSON>', '<EMAIL>'),
  ('<PERSON>', '<EMAIL>'),
  ('<PERSON>', '<EMAIL>'),
  ('<PERSON>', '<EMAIL>'),
  ('<PERSON>', '<EMAIL>'),
  ('<PERSON>', '<EMAIL>'),
  ('<PERSON>', '<EMAIL>'),
  ('<PERSON> <PERSON>', '<EMAIL>'),
  ('<PERSON>', '<EMAIL>'),
  ('<PERSON> <PERSON>', '<EMAIL>'),
  ('<PERSON> <PERSON>', '<EMAIL>'),
  ('<PERSON>', '<EMAIL>'),
  ('<PERSON>', '<EMAIL>'),
  ('Quinn Lopez', '<EMAIL>'),
  ('Rose Thompson', '<EMAIL>'),
  ('Samuel Perez', '<EMAIL>'),
  ('Tara Scott', '<EMAIL>');
